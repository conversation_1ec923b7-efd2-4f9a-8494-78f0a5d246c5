//-------------------------------------------------------------------------------
#define PWMDRIVER_C
#include	".\head\Config.h"

// 全局变量定义 - 只在此文件中定义一次
unsigned char duty = 0;           // 当前PWM占空比（百分比）
unsigned char count = 0;
unsigned char high_count = 0;

// PWM采样中断处理函数
void Timer1_INT(void) interrupt 3 {
    TF1 = 0;
    TR1 = 0;
    TL1 = 0xA0;  // 设置合适周期（约100us）
    TH1 = 0xFF;
    TR1 = 1;

    count++;
    if (PWM_IN) high_count++;

    if (count >= 100) {  // 采样周期结束（10ms）
        duty = (high_count * 100) / count;
        
        // 根据占空比设置速度级别
        DutyDecode(duty);
        
        // 清零计数器，准备下一轮采样
        count = 0;
        high_count = 0;
    }
}

// 根据占空比解码速度级别
void DutyDecode(unsigned char duty_value) {
    if (duty_value > 90) {
        LedWorkGroupStr.LedWorkMode = D_SPEED_LEVEL_6;
        KeyProcStr.SpeedLevel = 0x06;
    }
    else if (duty_value > 75) {
        LedWorkGroupStr.LedWorkMode = D_SPEED_LEVEL_5;
        KeyProcStr.SpeedLevel = 0x05;
    }
    else if (duty_value > 60) {
        LedWorkGroupStr.LedWorkMode = D_SPEED_LEVEL_4;
        KeyProcStr.SpeedLevel = 0x04;
    }
    else if (duty_value > 45) {
        LedWorkGroupStr.LedWorkMode = D_SPEED_LEVEL_3;
        KeyProcStr.SpeedLevel = 0x03;
    }
    else if (duty_value > 30) {
        LedWorkGroupStr.LedWorkMode = D_SPEED_LEVEL_2;
        KeyProcStr.SpeedLevel = 0x02;
    }
    else if (duty_value > 15) {
        LedWorkGroupStr.LedWorkMode = D_SPEED_LEVEL_1;
        KeyProcStr.SpeedLevel = 0x01;
    }
    else {
        // 使用 D_SPEED_LEVEL_1 替代 D_SPEED_LEVEL_OFF，因为后者未定义
        LedWorkGroupStr.LedWorkMode = D_SPEED_LEVEL_1; 
        KeyProcStr.SpeedLevel = 0x00;
    }
}
//-------------------------------------------------------------------------------

void Timer1Init(void)
{
    TMOD &= 0x0F;       // 清除T1控制位
    TMOD |= 0x10;       // T1为16位定时器
    TH1 = 0xFF;
    TL1 = 0xA0;
    TR1 = 1;            // 启动T1
    ET1 = 1;            // 开启T1中断
    EA = 1;             // 总中断开
}

// 添加 PwmDecode 函数，解决未解析的外部符号问题
void PwmDecode(void) {
    // 这个函数可能在 Main.c 中被调用
    // 根据实际需求实现功能
    DutyDecode(duty);
}
