/*==============================================================================
            Copyright(C) 2021-2029.  XXXXXX Tech. Co., Ltd.
--------------------------------------------------------------------------------
Project No.   : XXXXXX
File Name     : INTERRUPT..H	
Description   : 
              ；    
              ；   
Notice        :
             1:
             2:

Author        :  
Start Date    :  
Release Date  :  
Approve Date  :
Version       : V1.0
CheckSum      : XXXXXXX
Function List :
             1:
             2:

RevisionHistory:
Rev#  CheckSum   Date     Author     Comments(Function+Date)
-----+--------+----------+---------+--------------------------------------------
0.0 2021/10/08 Author Just build the function
0.1
==============================================================================*/


//------------------------------------------------------------------------------
#ifndef  INTERRUPT_H
#define  INTERRUPT_H


#ifdef INTERRUPT_C
   #define  INTERRUPT_EXT     
#else 
   #define  INTERRUPT_EXT   extern
#endif
//------------------------------------------------------------------------------

   

typedef struct
{
  unsigned char Task125usCnt;	
        
    unsigned char Task5msCnt;		
    unsigned char Task10msCnt;	
    unsigned char Task10msFlag;	

    unsigned char Task50msCnt;
    unsigned char Task50msFlag;  
    
    unsigned int  Task500msCnt;			
    unsigned char Task500msFlag;

    unsigned int  Task1000msCnt;			
    unsigned char Task1000msFlag;		

    unsigned char  TaskMonitorFlag;
    
}TIME_FLAG;
INTERRUPT_EXT TIME_FLAG  xdata TimeFlagStr;


INTERRUPT_EXT void InitInterruptRam(void);

// PWM采样相关
sbit PWM_IN = P3^0;   // PWM输入口

// 将变量定义改为声明（使用extern）
extern unsigned char duty;           // 当前PWM占空比（百分比）
extern unsigned char count;
extern unsigned char high_count;

#endif
