//------------------------------------------------------------------------------
#ifndef  PWMDRIVER_H
#define  PWMDRIVER_H


#ifdef PWMDRIVER_C
   #define  PWMDRIVER_EXT     
#else 
   #define  PWMDRIVER_EXT   extern
#endif
//------------------------------------------------------------------------------
   
PWMDRIVER_EXT void Timer1Init(void);
PWMDRIVER_EXT void DutyDecode(unsigned char duty_value);
PWMDRIVER_EXT void PwmDecode(void);  // 添加 PwmDecode 函数声明

// 全局变量声明
extern unsigned char duty;
extern unsigned char count;
extern unsigned char high_count;

#endif
