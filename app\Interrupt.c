/*==============================================================================
			Copyright(C) 2021-2029.  XXXXXX Tech. Co., Ltd.
--------------------------------------------------------------------------------
Project No.   : XXXXXX
File Name     : INTERRUPT..C
Description   :
			  ；
			  ；
Notice        :
			 1:
			 2:

Author        :
Start Date    :
Release Date  :
Approve Date  :
Version       : V1.0
CheckSum      : XXXXXXX
Function List :
			 1:
			 2:

RevisionHistory:
Rev#  CheckSum   Date     Author     Comments(Function+Date)
-----+--------+----------+---------+--------------------------------------------
0.0 2021/10/08 Author Just build the function
0.1
==============================================================================*/

//-------------------------------------------------------------------------------
#ifndef INTERRUPT_C
#define INTERRUPT_C
#endif
//-------------------------------------------------------------------------------

//---------------

#include ".\head\Config.h"
void UART_SendByte(unsigned char dat);

/****************************************************************************
 * Function Description:InitInterruptRam process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
void InitInterruptRam(void)
{
	TimeFlagStr.Task125usCnt = 0x00;

	TimeFlagStr.Task5msCnt = 0x00;
	TimeFlagStr.Task10msCnt = 0x00;
	TimeFlagStr.Task10msFlag = 0x00;

	TimeFlagStr.Task50msCnt = 0x00;
	TimeFlagStr.Task50msFlag = 0x00;

	TimeFlagStr.Task500msCnt = 0x00;
	TimeFlagStr.Task500msFlag = 0x00;

	TimeFlagStr.Task1000msCnt = 0x00;
	TimeFlagStr.Task1000msFlag = 0x00;
}

/****************************************************************************
 * Function Description:EX0_INT process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
void EX0_INT(void) interrupt 0 // 外部中断0服务程序:
{
	IE0 = 0; // TCON.1
	EX0 = 0; // IE2=EXF0.0,外部中断0中断请求标志
}

/****************************************************************************
 * Function Description:Timer0_INT process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
void Timer0_INT(void) interrupt 1 ////定时中断0服务程序,125US test is ok
{
	TF0 = 0; // TCON.5，由硬件自动清零
	TR0 = 0;
	TL0 = (unsigned char)((D_T0_125US_CNT >> 0) & 0x00FF); // t0的计数值
	TH0 = (unsigned char)((D_T0_125US_CNT >> 8) & 0x00FF); // t0的重载值;D_T0_125US_CNT
	TR0 = 1;

	////test
	// P1_5 = ~P1_5;
	////test

	TimeFlagStr.TaskMonitorFlag = 0x01; // 监测定时125us是否正常运行标志

	TimeFlagStr.Task125usCnt++;
	if (TimeFlagStr.Task125usCnt >= 8) // 125us*8=1ms,
	{
		TimeFlagStr.Task125usCnt = 0x00;

		TimeFlagStr.Task5msCnt++;
		if (TimeFlagStr.Task5msCnt >= 5) // 5ms
		{
			TimeFlagStr.Task5msCnt = 0x00;
		}

		TimeFlagStr.Task10msCnt++;
		if (TimeFlagStr.Task10msCnt >= 10) // 10ms
		{
			TimeFlagStr.Task10msCnt = 0x00;
			TimeFlagStr.Task10msFlag = 0x01;

			KeyDrvStr.KeyScanFlag = 0x01;

			TimeFlagStr.Task50msCnt++;
			if (TimeFlagStr.Task50msCnt >= 5) // 10ms*3=30ms
			{
				TimeFlagStr.Task50msCnt = 0x00;
				TimeFlagStr.Task50msFlag = 0x01;

				Uart.Cmd = RURN;
				Uart.TxdResponseFlag = 0x01; // 定时发送
				Uart.RxdCnt = 0x00;
				Uart.RxdFSM = 0x00;
				REN = 1; // 发送使能
			}

			// 时基计数
			TimeFlagStr.Task500msCnt++; // every 10ms interrupt,0.5s need 50 times
			if (TimeFlagStr.Task500msCnt >= 50)
			{
				TimeFlagStr.Task500msCnt = 0x00;
				TimeFlagStr.Task500msFlag = 0x01;

				TimeFlagStr.Task1000msCnt++; // every 500ms interrupt,1s need 2 times
				if (TimeFlagStr.Task1000msCnt >= 2)
				{
					TimeFlagStr.Task1000msCnt = 0x00;
					TimeFlagStr.Task1000msFlag = 0x01;
				}
			}
		}

		//        if(LedWorkGroupStr.Led1FlashMode == 0x01)
		//        {
		//            LedWorkGroupStr.Led1SlowFlashCnt = 0x00;
		//            LedWorkGroupStr.Led1FastFlashCnt++;
		//            if(LedWorkGroupStr.Led1FastFlashCnt >= 16)						//20
		//            {
		//                LedWorkGroupStr.Led1FastFlashCnt = 0x00;
		//            }
		//        }
		//        else if(LedWorkGroupStr.Led1FlashMode == 0x02)
		//        {
		//            LedWorkGroupStr.Led1FastFlashCnt = 0x00;
		//            LedWorkGroupStr.Led1SlowFlashCnt++;
		//            if(LedWorkGroupStr.Led1SlowFlashCnt >= 48)						//50
		//            {
		//                LedWorkGroupStr.Led1SlowFlashCnt = 0x00;
		//            }
		//        }
		//        else
		//        {
		//            LedWorkGroupStr.Led1SlowFlashCnt = 0x00;
		//            LedWorkGroupStr.Led1FastFlashCnt = 0x00;
		//        }
	}
}

/****************************************************************************
 * Function Description:EX1_INT process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
void EX1_INT(void) interrupt 2 // 外部中断1服务程序
{
	IE1 = 0; // TCON.1
	EX1 = 0; // IE2=EXF0.0,外部中断2中断请求标志
}

/****************************************************************************
 * Function Description:Timer1_INT process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
// 注释掉这个函数，因为它已经在 PwmDriver.c 中定义
/*
void Timer1_INT(void) interrupt 3 // 定时中断1服务程序:
{
    TF1 = 0;
    TR1 = 0;
    TL1 = 0xA0;  // 设置合适周期（约100us）
    TH1 = 0xFF;
    TR1 = 1;

    count++;
    if (PWM_IN) high_count++;

    if (count >= 95) {  // 采样周期结束（约10ms）
            duty = (high_count * 100) / count;

            // 占空比计算完成后可根据需要进一步处理 duty 变量
            count = 0;
            high_count = 0;
    }
}
*/



///****************************************************************************
// * Function Description:EUART0_INT process
// * Input parameter     :void
// * Output paramter     :void
// ****************************************************************************/
// void EUART0_INT(void) interrupt 4
//{
//    unsigned char xdata TempSBUF;

//    // ========== 接收中断 ==========
//    if (RI)
//    {
//				LED1 ^= 1;
//        RI = 0;
//        TempSBUF = SBUF;
//				UART_SendByte(TempSBUF);
//        switch (Uart.RxdFSM)
//        {
//            case 0X00: // 等待帧头1 (0x55)
//                if (TempSBUF == 0x55)
//                {
//                    Uart.RxdData[0] = TempSBUF;
//                    Uart.RxdCnt = 1;
//                    Uart.RxdFSM = 1;
//										UART_SendByte(TempSBUF);
//                }
//                else
//                {
//                    Uart.RxdCnt = 0;
//                    Uart.RxdFSM = 0;
//                }
//								UART_SendByte(TempSBUF); // 测试回传当前值
//                break;

//            case 0X01: // 等待帧头2 (0xAA)
//                if (TempSBUF == 0xAA)
//                {
//                    Uart.RxdData[1] = TempSBUF;
//                    Uart.RxdCnt = 2;
//                    Uart.RxdFSM = 2;
//                }
//                else
//                {
//                    Uart.RxdCnt = 0;
//                    Uart.RxdFSM = 0;
//                }
//								UART_SendByte(TempSBUF);
//                break;

//            case 0X02: // 接收命令字
//                Uart.RxdData[2] = TempSBUF;
//                Uart.RxdCnt = 3;
//                Uart.RxdFSM = 3;
//								UART_SendByte(TempSBUF);
//                break;

//            case 0X03: // 接收校验码
//                Uart.RxdData[3] = TempSBUF;
//                Uart.RxdCnt = 4;
//                Uart.RxdFSM = 0;
//								Uart.RxdResponseFlag = 0x01;
//								CommDealResponse(); //接收结束后直接校验解析
//								UART_SendByte(TempSBUF);
//								UART_SendByte(Uart.RxdResponseFlag);
//                break;

//            default:
//                Uart.RxdCnt = 0;
//                Uart.RxdFSM = 0;
//                break;
//        }
//    }

//
//	// ========== 发送中断 ==========
//    if (TI)
//    {
//				LED3 ^= 1;
//        TI = 0;

//        if (Uart.TxdDataCnt < Uart.TxdDataLength)
//        {
//            Uart.TxdDataCnt++;
//            SBUF = Uart.TxdData[Uart.TxdDataCnt];
//        }
//        else
//        {
//            REN = 1; // 启用接收
//        }
//    }
//
//}

void EUART0_INT(void) interrupt 4 // EUART中断服务程序
{
	unsigned char xdata TempSBUF;

	if (RI)
	{
		RI = 0;
		
		TempSBUF = SBUF;		// 串口数据寄存器
		Uart.RxdDataLength = 4; // Master data package : Fixed 4 Byte
		
		if (Uart.RxdFSM == 0x00)
		{
			switch (TempSBUF)
			{
			case HEAD_55: // 同步头(帧)0x55
				Uart.RxdData[Uart.RxdCnt] = TempSBUF;
				Uart.RxdCnt++;
				Uart.RxdFSM = 0x01;
				
				break;

			default:
				Uart.Cmd = 0x00;
				Uart.RxdCnt = 0x0;
				Uart.RxdFSM = 0x00;
				break;
			}
		}
		else if (Uart.RxdFSM == 0x01)
		{
			switch (TempSBUF)
			{
			case HEAD_AA: // 同步头(帧)0xAA
				Uart.RxdData[Uart.RxdCnt] = TempSBUF;
				Uart.RxdCnt++;
				Uart.RxdFSM = 0x02;
				
				break;

			default:
				Uart.Cmd = 0x00;
				Uart.RxdCnt = 0x0;
				Uart.RxdFSM = 0x00;
				break;
			}
		}
		else if (Uart.RxdFSM == 0x02)
		{
			Uart.RxdData[Uart.RxdCnt] = TempSBUF;
			Uart.RxdCnt++;

			if (Uart.RxdCnt >= Uart.RxdDataLength)
			{
				Uart.RxdResponseFlag = 0x01; // 接收10个字节ok!!!
				Uart.RxdCnt = 0x00;
				Uart.RxdFSM = 0x00;
				
				REN = 0; // 发送使能
			}
		}
		else
		{
			Uart.RxdCnt = 0x00;
			Uart.RxdFSM = 0x00;
			REN = 1; // 接收使能
		}
	}

	if (TI)
	{
		TI = 0;

		if (Uart.TxdDataCnt < Uart.TxdDataLength)
		{
			Uart.TxdDataCnt++;
			SBUF = Uart.TxdData[Uart.TxdDataCnt]; // 串口数据寄存器
		}
		else
		{
			REN = 1; // 接收使能
		}
	}
}

// void UART_SendByte(unsigned char dat) //测试回传数据
//{
//     while (!TI);  // 等待上一次发送完成
//     TI = 0;
//     SBUF = dat;
// }

// void EUART0_INT(void) interrupt 4        						//EUART中断服务程序
//{
//		unsigned char data TempSBUF;

//    if(RI)
//    {
//        RI = 0;
//
//				TempSBUF  = SBUF;         									//串口数据寄存器
//				Uart.RxdDataLength = 11; 										//Master data package : Fixed 11 Byte
//
//	      if(Uart.RxdFSM == 0)
//		    {
//						switch(TempSBUF)
//						{
//								case HEAD:            						 	//同步头(帧)0x55
//										Uart.RxdData[Uart.RxdCnt] = TempSBUF;
//										Uart.RxdCnt++;
//										Uart.RxdFSM = 0x01;
//								break;
//
//								default:
//										Uart.Cmd   = 0x00;
//										Uart.RxdCnt = 0x0;
//										Uart.RxdFSM = 0x00;
//								break;
//						}
//				}
//				else if(Uart.RxdFSM == 0x01)
//				{
//						switch(TempSBUF)
//						{
//								case CHOICE:												//0x10       /* 选型 */
//								case STANDBY:												//0x11       /* 停止状态 */
//								case STARTUP:  											//0x12       /* 脱水状态 */
//								case OPENLOOP:											//0x14       /* 脱水检测指令状态 */
//								case RURN:													//0xAA       /* 洗涤动作状态1 */
//								case ERROR:													//0x24       /* ERROR检测 */
//								case ERRRESET:											//0xFE       /* 异常复位 */
//										Uart.Cmd = TempSBUF;
//										Uart.RxdData[Uart.RxdCnt] = TempSBUF;
//										Uart.RxdCnt++;
//										Uart.RxdFSM = 0x02;
//								break;
//
//								default:
//										Uart.Cmd = 0x00;
//										Uart.RxdCnt = 0x00;
//										Uart.RxdFSM = 0x00;
//								break;
//						}
//				}
//				else if(Uart.RxdFSM == 0x02)
//				{
//						Uart.RxdData[Uart.RxdCnt] = TempSBUF;
//						Uart.RxdCnt++;
//
//						if(Uart.RxdCnt >= Uart.RxdDataLength)
//						{
//								Uart.RxdResponseFlag = 0x01;			//接收10个字节ok!!!
//								Uart.RxdCnt = 0x00;
//								Uart.RxdFSM = 0x00;
//								REN = 0;													//发送使能
//						}
//				}
//				else
//				{
//						Uart.RxdCnt = 0x00;
//						Uart.RxdFSM = 0x00;
//					  REN = 1;													 		//接收使能
//				}
//    }

//
//		if(TI)
//    {
//				TI = 0;
//
//				if(Uart.TxdDataCnt < Uart.TxdDataLength)
//				{
//						Uart.TxdDataCnt++;
//						SBUF = Uart.TxdData[Uart.TxdDataCnt];  //串口数据寄存器
//				}
//				else
//				{
//						REN = 1;													 			//接收使能
//				}
//    }
//
//}

/****************************************************************************
 * Function Description:Timer2_INT process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
void Timer2_INT(void) interrupt 5 // 定时中断2服务程序
{
	EXF2 = 0;
	TF2 = 0; // EXF2=T2CON.6 TF2=T2CON.7
}

/****************************************************************************
 * Function Description:EX2_INT process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
void EX2_INT(void) interrupt 9 // 外部中断2服务程序
{
	EXF0 = Bin(00000100);  // IE2=EXF0.0,清外部中断2中断请求标志，BIT3，BIT2，=01,下降沿触发
	IEN1 &= Bin(11111011); // BIT2,EX2 = 0,关闭外部中断2
}

/****************************************************************************
 * Function Description:SCM_INT process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
void SCM_INT(void) interrupt 11
{
	CLKCON &= Bin(11101111); // SCMIF，必须由硬件自动清零
}

/****************************************************************************
 * Function Description:PWM_INT process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
void PWM_INT(void) interrupt 12 // PWM中断服务程序
{
	PWMCON &= Bin(11111101); // PWMIF=0;
}

/****************************************************************************
 * Function Description:ELPD_INT process
 * Input parameter     :void
 * Output paramter     :void
 ****************************************************************************/
void ELPD_INT(void) interrupt 14 // LPD中断服务程序
{
	LPDCON &= Bin(10111111); // LPDF=0;
}
