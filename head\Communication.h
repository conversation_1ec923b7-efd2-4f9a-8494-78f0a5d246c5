/*==============================================================================
            Copyright(C) 1997-2019.  Sinowealth Tech. Co., Ltd.
--------------------------------------------------------------------------------
Project No.   : XXX
File Name     : COMMUNICATION..H	
Description   : 
              £»    
              £»   
Notice        :
             1:
             2:

Author        : <PERSON> (email: <EMAIL>)
Start Date    : 2022/07/18	
Release Date  :  
Approve Date  :
Version       : V1.0
CheckSum      : XXXX
Function List :
             1:
             2:

RevisionHistory:
Rev#  CheckSum   Date     Author     Comments(Function+Date)
-----+--------+----------+---------+--------------------------------------------
0.0 2022/07/18 James Just build the function
0.1
==============================================================================*/


//------------------------------------------------------------------------------
#ifndef  COMMUNICATION_H
#define  COMMUNICATION_H
extern unsigned char g_last_speed_level; // 新增声明


#ifdef COMMUNICATION_C
   #define  COMMUNICATION_EXT     
#else 
   #define  COMMUNICATION_EXT   extern
#endif
//------------------------------------------------------------------------------

//--------------

#define    ACK							0x06
#define    NCK							0x15
	 
#define    HEAD             0x55       //同步头(帧)0x55 
#define    HEAD_55          0x55       //同步头(帧)0x55 	 
#define    HEAD_AA          0xAA       //同步头(帧)0xAA 	 
#define    CHOICE           0x10       //先择模式
#define    STANDBY          0x11       //待机状态
#define    STARTUP          0x12       //启动状态
#define    OPENLOOP         0x14       //开环状态
#define    RURN             0xAA       //运行 
#define    ERROR            0x24       //ERROR检测 
#define    ERRRESET         0xFE       //异常复位 

	 
#define    SoftVerison      0x0055			//Version5.5，发送0x55

	 
typedef struct
{
    unsigned char RxdData[20];						
    unsigned char TxdData[20];						
	
    unsigned char RxdFSM;
    unsigned char RxdCnt;
    unsigned char RxdDataLength;
    unsigned char RxdTimeCnt;
    unsigned int  RxdTimeOutCnt;
    
    unsigned char TxdDataLength;
    unsigned char TxdDataCnt;
    
    unsigned char RxdCrcHigh;
    unsigned char RxdCrcLow;
    unsigned char TxdCrcHigh;
    unsigned char TxdCrcLow;
	
    unsigned int  RxdCheckData;
    unsigned int  TxdCheckData;
		
    unsigned char Cmd;
    unsigned char RxdResponseFlag;
    unsigned char TxdResponseFlag;		
		
//		unsigned int  crcRecVal;
//    unsigned int  crcCalVal;		
			
} UART_GROUP;
COMMUNICATION_EXT  UART_GROUP  xdata Uart;


COMMUNICATION_EXT unsigned char CalcChecksum(unsigned char *CS_Buffer, unsigned char Len);
	
// ===== 添加函数声明 =====
void TxdDataProc(unsigned char Ack); // 发送数据帧函数声明
//COMMUNICATION_EXT code unsigned int crc16_ccitt_table[];
//COMMUNICATION_EXT unsigned int crc16_x25(unsigned char *cata, unsigned char length);
COMMUNICATION_EXT void CommDealResponse(void);	
COMMUNICATION_EXT void CommFunProc(void);
COMMUNICATION_EXT void PwmDecode(unsigned char duty_shadow);


#endif
