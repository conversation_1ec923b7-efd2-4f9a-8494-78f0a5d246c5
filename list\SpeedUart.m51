BL51 BANKED LINKER/LOCATER V6.22.2.0                                                    06/24/2025  19:57:05  PAGE 1


BL51 BANKED LINKER/LOCATER V6.22.2.0, INVOKED BY:
C:\KEIL_V5\C51\BIN\BL51.EXE .\output\STARTUP.obj, .\output\Main.obj, .\output\Mcu.obj, .\output\Interrupt.obj, .\output\
>> LedApp.obj, .\output\KeyApp.obj, .\output\Communication.obj, .\output\LedDriver.obj, .\output\KeyDriver.obj, .\output
>> \UartDriver.obj, .\output\PwmDriver.obj TO .\output\SpeedUart PRINT (.\list\SpeedUart.m51) RAMSIZE (256)


MEMORY MODEL: LARGE


INPUT MODULES INCLUDED:
  .\output\STARTUP.obj (?C_STARTUP)
  .\output\Main.obj (MAIN)
  .\output\Mcu.obj (MCU)
  .\output\Interrupt.obj (INTERRUPT)
  .\output\LedApp.obj (LEDAPP)
  .\output\KeyApp.obj (KEYAPP)
  .\output\Communication.obj (COMMUNICATION)
  .\output\LedDriver.obj (LEDDRIVER)
  .\output\KeyDriver.obj (KEYDRIVER)
  .\output\UartDriver.obj (UARTDRIVER)
  .\output\PwmDriver.obj (PWMDRIVER)
  C:\KEIL_V5\C51\LIB\C51L.LIB (?C_INIT)
  C:\KEIL_V5\C51\LIB\C51L.LIB (?C?CLDOPTR)
  C:\KEIL_V5\C51\LIB\C51L.LIB (?C?UIDIV)
  C:\KEIL_V5\C51\LIB\C51L.LIB (?C?SLDIV)
  C:\KEIL_V5\C51\LIB\C51L.LIB (?C?ULDIV)


LINK MAP OF MODULE:  .\output\SpeedUart (?C_STARTUP)


            TYPE    BASE      LENGTH    RELOCATION   SEGMENT NAME
            -----------------------------------------------------

            * * * * * * *   D A T A   M E M O R Y   * * * * * * *
            REG     0000H     0008H     ABSOLUTE     "REG BANK 0"
            IDATA   0008H     0001H     UNIT         ?STACK

            * * * * * * *  X D A T A   M E M O R Y  * * * * * * *
            XDATA   0000H     003BH     UNIT         ?XD?COMMUNICATION
            XDATA   003BH     000DH     UNIT         ?XD?INTERRUPT
            XDATA   0048H     000DH     UNIT         ?XD?KEYDRIVER
            XDATA   0055H     0005H     UNIT         _XDATA_GROUP_
            XDATA   005AH     0003H     UNIT         ?XD?LEDAPP
            XDATA   005DH     0003H     UNIT         ?XD?PWMDRIVER
            XDATA   0060H     0002H     UNIT         ?XD?KEYAPP
            XDATA   0062H     0001H     UNIT         ?XD?MAIN

            * * * * * * *   C O D E   M E M O R Y   * * * * * * *
            CODE    0000H     0003H     ABSOLUTE     
            CODE    0003H     0003H     ABSOLUTE     
            CODE    0006H     0005H     UNIT         ?PR?EX0_INT?INTERRUPT
            CODE    000BH     0003H     ABSOLUTE     
            CODE    000EH     0005H     UNIT         ?PR?EX1_INT?INTERRUPT
            CODE    0013H     0003H     ABSOLUTE     
            CODE    0016H     0005H     UNIT         ?PR?TIMER2_INT?INTERRUPT
            CODE    001BH     0003H     ABSOLUTE     
* OVERLAP * CODE    001BH     0003H     ABSOLUTE     
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  19:57:05  PAGE 2


            CODE    001EH     0004H     UNIT         ?PR?SCM_INT?INTERRUPT
                    0022H     0001H                  *** GAP ***
            CODE    0023H     0003H     ABSOLUTE     
            CODE    0026H     0004H     UNIT         ?PR?PWM_INT?INTERRUPT
                    002AH     0001H                  *** GAP ***
            CODE    002BH     0003H     ABSOLUTE     
            CODE    002EH     001AH     UNIT         ?PR?INITT0?MCU
            CODE    0048H     0003H     UNIT         ?PR?INITCPUACSET?MCU
            CODE    004BH     0003H     ABSOLUTE     
            CODE    004EH     000DH     UNIT         ?PR?MONITORCPUTIMER?MCU
            CODE    005BH     0003H     ABSOLUTE     
            CODE    005EH     0004H     UNIT         ?PR?ELPD_INT?INTERRUPT
                    0062H     0001H                  *** GAP ***
            CODE    0063H     0003H     ABSOLUTE     
            CODE    0066H     000CH     UNIT         ?PR?INITINTERRUPTRAM?INTERRUPT
                    0072H     0001H                  *** GAP ***
            CODE    0073H     0003H     ABSOLUTE     
            CODE    0076H     0150H     UNIT         ?C?LIB_CODE
            CODE    01C6H     013DH     UNIT         ?PR?KEYSCAN?KEYDRIVER
            CODE    0303H     00D0H     UNIT         ?PR?EUART0_INT?INTERRUPT
            CODE    03D3H     00BFH     UNIT         ?PR?_TXDDATAPROC?COMMUNICATION
            CODE    0492H     00BCH     UNIT         ?PR?TIMER0_INT?INTERRUPT
            CODE    054EH     008CH     UNIT         ?C_C51STARTUP
            CODE    05DAH     008BH     UNIT         ?PR?KEYPROC?KEYAPP
            CODE    0665H     007AH     UNIT         ?PR?TIMER1_INT?PWMDRIVER
            CODE    06DFH     0077H     UNIT         ?PR?_DUTYDECODE?PWMDRIVER
            CODE    0756H     0076H     UNIT         ?PR?LEDAPP?LEDAPP
            CODE    07CCH     0076H     UNIT         ?PR?COMMDEALRESPONSE?COMMUNICATION
            CODE    0842H     0073H     UNIT         ?PR?TIMER1_INT?INTERRUPT
            CODE    08B5H     005CH     UNIT         ?PR?INITKEYSCANRAM?KEYDRIVER
            CODE    0911H     005AH     UNIT         ?PR?MAIN?MAIN
            CODE    096BH     0044H     UNIT         ?C?LDIV
            CODE    09AFH     0040H     UNIT         ?PR?_UART0CALBAUDRATE?UARTDRIVER
            CODE    09EFH     003AH     UNIT         ?PR?INTERRUPT
            CODE    0A29H     002CH     UNIT         ?PR?_CALCCHECKSUM?COMMUNICATION
            CODE    0A55H     0029H     UNIT         ?PR?UART0INIT?UARTDRIVER
            CODE    0A7EH     0022H     UNIT         ?PR?KEYAPP
            CODE    0AA0H     0021H     UNIT         ?PR?_DELAYNMS?MCU
            CODE    0AC1H     001AH     UNIT         ?PR?COMMFUNPROC?COMMUNICATION
            CODE    0ADBH     0019H     UNIT         ?PR?INITPORT?MCU
            CODE    0AF4H     0015H     UNIT         ?C_INITSEG
            CODE    0B09H     0014H     UNIT         ?PR?INITT1?MCU
            CODE    0B1DH     0013H     UNIT         ?PR?TIMER1INIT?PWMDRIVER
            CODE    0B30H     0012H     UNIT         ?PR?INITALLLEDOFF?LEDDRIVER
            CODE    0B42H     000FH     UNIT         ?PR?COMMUNICATION
            CODE    0B51H     000EH     UNIT         ?PR?INITSYS?MCU
            CODE    0B5FH     000CH     UNIT         ?PR?KEYDRIVER
            CODE    0B6BH     000AH     UNIT         ?PR?INITINTERRUPTPRIORITYLEVEL?MCU
            CODE    0B75H     0009H     UNIT         ?PR?LED1_WORKON?LEDDRIVER
            CODE    0B7EH     0009H     UNIT         ?PR?LED1_WORKOFF?LEDDRIVER
            CODE    0B87H     0009H     UNIT         ?PR?LED2_WORKON?LEDDRIVER
            CODE    0B90H     0009H     UNIT         ?PR?LED2_WORKOFF?LEDDRIVER
            CODE    0B99H     0009H     UNIT         ?PR?LED3_WORKON?LEDDRIVER
            CODE    0BA2H     0009H     UNIT         ?PR?LED3_WORKOFF?LEDDRIVER
            CODE    0BABH     0009H     UNIT         ?PR?LED4_WORKON?LEDDRIVER
            CODE    0BB4H     0009H     UNIT         ?PR?LED4_WORKOFF?LEDDRIVER
            CODE    0BBDH     0009H     UNIT         ?PR?LED5_WORKON?LEDDRIVER
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  19:57:05  PAGE 3


            CODE    0BC6H     0009H     UNIT         ?PR?LED5_WORKOFF?LEDDRIVER
            CODE    0BCFH     0009H     UNIT         ?PR?LED6_WORKON?LEDDRIVER
            CODE    0BD8H     0009H     UNIT         ?PR?LED6_WORKOFF?LEDDRIVER
            CODE    0BE1H     0008H     UNIT         ?PR?INITPWM?MCU
            CODE    0BE9H     0007H     UNIT         ?PR?INITINT1?MCU
            CODE    0BF0H     0007H     UNIT         ?PR?INITINT2?MCU
            CODE    0BF7H     0007H     UNIT         ?PR?EX2_INT?INTERRUPT
            CODE    0BFEH     0006H     UNIT         ?PR?INITKEYPROCRAM?KEYAPP
            CODE    0C04H     0006H     UNIT         ?PR?INITALLLEDDRIVERRAM?LEDDRIVER
            CODE    0C0AH     0003H     UNIT         ?PR?INITALLLEDRAMAPP?LEDAPP



OVERLAY MAP OF MODULE:   .\output\SpeedUart (?C_STARTUP)


SEGMENT                                      XDATA_GROUP
  +--> CALLED SEGMENT                      START    LENGTH
----------------------------------------------------------
?C_C51STARTUP                              -----    -----
  +--> ?PR?MAIN?MAIN
  +--> ?C_INITSEG

?PR?MAIN?MAIN                              -----    -----
  +--> ?PR?INITSYS?MCU
  +--> ?PR?_DELAYNMS?MCU
  +--> ?PR?INITPORT?MCU
  +--> ?PR?INITKEYSCANRAM?KEYDRIVER
  +--> ?PR?INITKEYPROCRAM?KEYAPP
  +--> ?PR?INITINTERRUPTRAM?INTERRUPT
  +--> ?PR?INITALLLEDOFF?LEDDRIVER
  +--> ?PR?INITALLLEDRAMAPP?LEDAPP
  +--> ?PR?INITT0?MCU
  +--> ?PR?INITT1?MCU
  +--> ?PR?INITINT1?MCU
  +--> ?PR?INITINT2?MCU
  +--> ?PR?INITPWM?MCU
  +--> ?PR?UART0INIT?UARTDRIVER
  +--> ?PR?TIMER1INIT?PWMDRIVER
  +--> ?PR?MONITORCPUTIMER?MCU
  +--> ?PR?INITCPUACSET?MCU
  +--> ?PR?KEYSCAN?KEYDRIVER
  +--> ?PR?KEYPROC?KEYAPP
  +--> ?PR?LEDAPP?LEDAPP
  +--> ?PR?COMMFUNPROC?COMMUNICATION

?PR?INITSYS?MCU                            -----    -----
  +--> ?PR?INITINTERRUPTPRIORITYLEVEL?MCU

?PR?INITINTERRUPTRAM?INTERRUPT             -----    -----
  +--> ?PR?INTERRUPT

?PR?INITALLLEDOFF?LEDDRIVER                -----    -----
  +--> ?PR?LED1_WORKOFF?LEDDRIVER
  +--> ?PR?LED2_WORKOFF?LEDDRIVER
  +--> ?PR?LED3_WORKOFF?LEDDRIVER
  +--> ?PR?LED4_WORKOFF?LEDDRIVER
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  19:57:05  PAGE 4


  +--> ?PR?LED5_WORKOFF?LEDDRIVER
  +--> ?PR?LED6_WORKOFF?LEDDRIVER

?PR?INITALLLEDRAMAPP?LEDAPP                -----    -----
  +--> ?PR?INITALLLEDDRIVERRAM?LEDDRIVER

?PR?UART0INIT?UARTDRIVER                   -----    -----
  +--> ?PR?_UART0CALBAUDRATE?UARTDRIVER

?PR?_UART0CALBAUDRATE?UARTDRIVER           0055H    0002H

?PR?KEYSCAN?KEYDRIVER                      0055H    0002H
  +--> ?PR?KEYDRIVER

?PR?KEYPROC?KEYAPP                         -----    -----
  +--> ?PR?KEYAPP
  +--> ?PR?_TXDDATAPROC?COMMUNICATION

?PR?_TXDDATAPROC?COMMUNICATION             0055H    0001H
  +--> ?PR?COMMUNICATION
  +--> ?PR?_CALCCHECKSUM?COMMUNICATION

?PR?_CALCCHECKSUM?COMMUNICATION            0056H    0003H

?PR?LEDAPP?LEDAPP                          -----    -----
  +--> ?PR?INITALLLEDOFF?LEDDRIVER
  +--> ?PR?LED1_WORKON?LEDDRIVER
  +--> ?PR?LED2_WORKON?LEDDRIVER
  +--> ?PR?LED3_WORKON?LEDDRIVER
  +--> ?PR?LED4_WORKON?LEDDRIVER
  +--> ?PR?LED5_WORKON?LEDDRIVER
  +--> ?PR?LED6_WORKON?LEDDRIVER

?PR?COMMFUNPROC?COMMUNICATION              -----    -----
  +--> ?PR?_TXDDATAPROC?COMMUNICATION
  +--> ?PR?COMMDEALRESPONSE?COMMUNICATION

?PR?COMMDEALRESPONSE?COMMUNICATION         -----    -----
  +--> ?PR?_CALCCHECKSUM?COMMUNICATION
  +--> ?PR?_TXDDATAPROC?COMMUNICATION

*** NEW ROOT ***************************************************

?PR?TIMER0_INT?INTERRUPT                   -----    -----
  +--> ?PR?INTERRUPT

*** NEW ROOT ***************************************************

?PR?EUART0_INT?INTERRUPT                   0059H    0001H
  +--> ?PR?INTERRUPT

*** NEW ROOT ***************************************************

?PR?TIMER1_INT?PWMDRIVER                   -----    -----
  +--> ?PR?_DUTYDECODE?PWMDRIVER


BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  19:57:05  PAGE 5



UNRESOLVED EXTERNAL SYMBOLS:
   _PWMDECODE



IGNORED SYMBOLS:
   TIMER1_INT



SYMBOL TABLE OF MODULE:  .\output\SpeedUart (?C_STARTUP)

  VALUE           TYPE          NAME
  ----------------------------------

  -------         MODULE        ?C_STARTUP
  C:054EH         SEGMENT       ?C_C51STARTUP
  I:0008H         SEGMENT       ?STACK
  C:0000H         PUBLIC        ?C_STARTUP
  D:00E0H         SYMBOL        ACC
  D:00F0H         SYMBOL        B
  N:3026H         SYMBOL        CODE_SIZE
  D:0083H         SYMBOL        DPH
  D:0082H         SYMBOL        DPL
  N:0F9AH         SYMBOL        FILLING_A5_NUM
  C:055AH         SYMBOL        FILL_CODE
  N:0000H         SYMBOL        IBPSTACK
  N:0100H         SYMBOL        IBPSTACKTOP
  N:0080H         SYMBOL        IDATALEN
  C:0551H         SYMBOL        IDATALOOP
  N:0000H         SYMBOL        PBPSTACK
  N:0000H         SYMBOL        PBPSTACKTOP
  N:0000H         SYMBOL        PDATALEN
  N:0000H         SYMBOL        PDATASTART
  N:0000H         SYMBOL        PPAGE
  N:0000H         SYMBOL        PPAGEENABLE
  D:00A0H         SYMBOL        PPAGE_SFR
  N:3FC0H         SYMBOL        ROM_SIZE
  D:0081H         SYMBOL        SP
  C:054EH         SYMBOL        STARTUP1
  N:0000H         SYMBOL        XBPSTACK
  N:0000H         SYMBOL        XBPSTACKTOP
  N:0000H         SYMBOL        XDATALEN
  N:0000H         SYMBOL        XDATASTART
  C:0000H         LINE#         107
  C:054EH         LINE#         114
  C:0550H         LINE#         115
  C:0551H         LINE#         116
  C:0552H         LINE#         117
  C:0554H         LINE#         166
  C:0557H         LINE#         170
  -------         ENDMOD        ?C_STARTUP

  -------         MODULE        MAIN
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  19:57:05  PAGE 6


  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  B:00A8H.7       PUBLIC        EA
  D:00A8H         PUBLIC        IEN0
  D:00A9H         PUBLIC        IEN1
  C:0911H         PUBLIC        main
  X:0062H         PUBLIC        duty_shadow
  -------         PROC          MAIN
  C:0911H         LINE#         50
  C:0911H         LINE#         51
  C:0911H         LINE#         52
  C:0913H         LINE#         53
  C:0916H         LINE#         54
  C:091DH         LINE#         55
  C:0920H         LINE#         56
  C:0923H         LINE#         57
  C:0926H         LINE#         58
  C:0929H         LINE#         59
  C:092CH         LINE#         60
  C:092FH         LINE#         61
  C:0932H         LINE#         62
  C:0935H         LINE#         63
  C:0938H         LINE#         64
  C:093BH         LINE#         65
  C:093EH         LINE#         66
  C:0941H         LINE#         67
  C:0944H         LINE#         68
  C:0947H         LINE#         69
  C:094AH         LINE#         70
  C:094CH         LINE#         81
  C:094CH         LINE#         82
  C:094CH         LINE#         83
  C:094FH         LINE#         84
  C:0952H         LINE#         85
  C:0955H         LINE#         86
  C:0958H         LINE#         87
  C:095BH         LINE#         88
  C:095EH         LINE#         89
  C:0966H         LINE#         90
  C:0969H         LINE#         91
  -------         ENDPROC       MAIN
  -------         ENDMOD        MAIN

  -------         MODULE        MCU
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:00EAH         PUBLIC        P1M0
  D:00E2H         PUBLIC        P1M1
  D:00ECH         PUBLIC        P3M0
  D:0090H         PUBLIC        P1
  D:00EDH         PUBLIC        P4M0
  D:00E4H         PUBLIC        P3M1
  D:00E5H         PUBLIC        P4M1
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  B:00A8H.7       PUBLIC        EA
  D:00A9H         PUBLIC        IEN1
  D:00B4H         PUBLIC        IPH0
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  19:57:05  PAGE 7


  D:00B5H         PUBLIC        IPH1
  D:00E8H         PUBLIC        EXF0
  D:00B8H         PUBLIC        IPL0
  D:00B9H         PUBLIC        IPL1
  C:002EH         PUBLIC        InitT0
  C:004EH         PUBLIC        MonitorCpuTimer
  C:0B09H         PUBLIC        InitT1
  C:0AA0H         PUBLIC        _DelayNms
  C:0BE9H         PUBLIC        InitINT1
  C:0BF0H         PUBLIC        InitINT2
  D:0089H         PUBLIC        TMOD
  D:0088H         PUBLIC        TCON
  D:00B1H         PUBLIC        RSTSTAT
  D:00D3H         PUBLIC        PWMD
  D:00B2H         PUBLIC        CLKCON
  C:0ADBH         PUBLIC        InitPort
  B:0088H.3       PUBLIC        IE1
  D:00B3H         PUBLIC        LPDCON
  D:00D2H         PUBLIC        PWMP
  B:0088H.5       PUBLIC        TF0
  B:0088H.7       PUBLIC        TF1
  C:0BE1H         PUBLIC        InitPwm
  D:008CH         PUBLIC        TH0
  D:008DH         PUBLIC        TH1
  B:0088H.2       PUBLIC        IT1
  B:00A8H.2       PUBLIC        EX1
  C:0B6BH         PUBLIC        InitInterruptPriorityLevel
  D:008AH         PUBLIC        TL0
  D:008BH         PUBLIC        TL1
  B:0088H.4       PUBLIC        TR0
  D:00D1H         PUBLIC        PWMCON
  C:0B51H         PUBLIC        InitSys
  B:0088H.6       PUBLIC        TR1
  D:00CEH         PUBLIC        TCON1
  C:0048H         PUBLIC        InitCpuACSet
  -------         PROC          INITSYS
  C:0B51H         LINE#         50
  C:0B51H         LINE#         51
  C:0B51H         LINE#         52
  C:0B53H         LINE#         53
  C:0B56H         LINE#         54
  C:0B58H         LINE#         55
  C:0B5BH         LINE#         56
  C:0B5EH         LINE#         57
  -------         ENDPROC       INITSYS
  -------         PROC          INITCPUACSET
  C:0048H         LINE#         64
  C:0048H         LINE#         65
  C:0048H         LINE#         66
  C:004AH         LINE#         67
  -------         ENDPROC       INITCPUACSET
  -------         PROC          INITPORT
  C:0ADBH         LINE#         74
  C:0ADBH         LINE#         75
  C:0ADBH         LINE#         76
  C:0ADEH         LINE#         77
  C:0AE1H         LINE#         78
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  19:57:05  PAGE 8


  C:0AE3H         LINE#         80
  C:0AE6H         LINE#         81
  C:0AE9H         LINE#         83
  C:0AEBH         LINE#         84
  C:0AEEH         LINE#         86
  C:0AF0H         LINE#         87
  C:0AF3H         LINE#         88
  -------         ENDPROC       INITPORT
  -------         PROC          INITINTERRUPTPRIORITYLEVEL
  C:0B6BH         LINE#         96
  C:0B6BH         LINE#         97
  C:0B6BH         LINE#         98
  C:0B6EH         LINE#         99
  C:0B70H         LINE#         100
  C:0B72H         LINE#         101
  C:0B74H         LINE#         102
  -------         ENDPROC       INITINTERRUPTPRIORITYLEVEL
  -------         PROC          INITT0
  C:002EH         LINE#         109
  C:002EH         LINE#         110
  C:002EH         LINE#         111
  C:0031H         LINE#         113
  C:0034H         LINE#         114
  C:0037H         LINE#         116
  C:003AH         LINE#         117
  C:003DH         LINE#         119
  C:0040H         LINE#         120
  C:0043H         LINE#         121
  C:0045H         LINE#         122
  C:0047H         LINE#         123
  -------         ENDPROC       INITT0
  -------         PROC          INITT1
  C:0B09H         LINE#         131
  C:0B09H         LINE#         132
  C:0B09H         LINE#         133
  C:0B0CH         LINE#         135
  C:0B0FH         LINE#         136
  C:0B0FH         LINE#         139
  C:0B12H         LINE#         140
  C:0B12H         LINE#         143
  C:0B15H         LINE#         144
  C:0B18H         LINE#         145
  C:0B1AH         LINE#         146
  C:0B1CH         LINE#         147
  -------         ENDPROC       INITT1
  -------         PROC          INITINT1
  C:0BE9H         LINE#         155
  C:0BE9H         LINE#         156
  C:0BE9H         LINE#         157
  C:0BEBH         LINE#         158
  C:0BEDH         LINE#         159
  C:0BEFH         LINE#         160
  -------         ENDPROC       INITINT1
  -------         PROC          INITINT2
  C:0BF0H         LINE#         167
  C:0BF0H         LINE#         168
  C:0BF0H         LINE#         169
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  19:57:05  PAGE 9


  C:0BF3H         LINE#         170
  C:0BF6H         LINE#         171
  -------         ENDPROC       INITINT2
  -------         PROC          INITPWM
  C:0BE1H         LINE#         179
  C:0BE1H         LINE#         180
  C:0BE1H         LINE#         185
  C:0BE4H         LINE#         186
  C:0BE6H         LINE#         187
  C:0BE8H         LINE#         188
  -------         ENDPROC       INITPWM
  -------         PROC          MONITORCPUTIMER
  C:004EH         LINE#         196
  C:004EH         LINE#         197
  C:004EH         LINE#         198
  C:0056H         LINE#         199
  C:0056H         LINE#         200
  C:0057H         LINE#         202
  C:005AH         LINE#         203
  C:005AH         LINE#         204
  -------         ENDPROC       MONITORCPUTIMER
  -------         PROC          _DELAYNMS
  D:0006H         SYMBOL        num
  -------         DO            
  D:0004H         SYMBOL        i
  D:0002H         SYMBOL        j
  -------         ENDDO         
  C:0AA0H         LINE#         212
  C:0AA0H         LINE#         213
  C:0AA0H         LINE#         216
  C:0AAAH         LINE#         217
  C:0AAAH         LINE#         218
  C:0AADH         LINE#         219
  C:0AADH         LINE#         220
  C:0AAEH         LINE#         221
  C:0AB9H         LINE#         222
  C:0AC0H         LINE#         223
  -------         ENDPROC       _DELAYNMS
  -------         ENDMOD        MCU

  -------         MODULE        INTERRUPT
  C:0000H         SYMBOL        _ICE_DUMMY_
  C:0303H         PUBLIC        EUART0_INT
  C:0492H         PUBLIC        Timer0_INT
  D:0090H         PUBLIC        P1
  C:0842H         PUBLIC        Timer1_INT
  C:0016H         PUBLIC        Timer2_INT
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  C:0066H         PUBLIC        InitInterruptRam
  D:00A9H         PUBLIC        IEN1
  D:00E8H         PUBLIC        EXF0
  B:00C8H.6       PUBLIC        EXF2
  C:0006H         PUBLIC        EX0_INT
  C:000EH         PUBLIC        EX1_INT
  B:0098H.0       PUBLIC        RI
  C:0BF7H         PUBLIC        EX2_INT
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  19:57:05  PAGE 10


  B:0098H.1       PUBLIC        TI
  C:001EH         PUBLIC        SCM_INT
  D:0099H         PUBLIC        SBUF
  D:00B2H         PUBLIC        CLKCON
  B:0088H.1       PUBLIC        IE0
  B:0088H.3       PUBLIC        IE1
  C:0026H         PUBLIC        PWM_INT
  D:00B3H         PUBLIC        LPDCON
  X:003BH         PUBLIC        TimeFlagStr
  B:0088H.5       PUBLIC        TF0
  B:0088H.7       PUBLIC        TF1
  B:00C8H.7       PUBLIC        TF2
  D:008CH         PUBLIC        TH0
  B:00A8H.0       PUBLIC        EX0
  D:008DH         PUBLIC        TH1
  B:00A8H.2       PUBLIC        EX1
  D:008AH         PUBLIC        TL0
  D:008BH         PUBLIC        TL1
  B:0088H.4       PUBLIC        TR0
  D:00D1H         PUBLIC        PWMCON
  B:0088H.6       PUBLIC        TR1
  B:0098H.4       PUBLIC        REN
  B:00B0H.0       PUBLIC        PWM_IN
  C:005EH         PUBLIC        ELPD_INT
  C:09EFH         SYMBOL        Com0028
  C:09EFH         SYMBOL        L?0041
  C:09FBH         SYMBOL        L?0042
  C:0A09H         SYMBOL        L?0043
  C:0A11H         SYMBOL        L?0044
  C:0A14H         SYMBOL        L?0045
  C:0A1EH         SYMBOL        L?0046
  -------         PROC          COM0028
  -------         ENDPROC       COM0028
  -------         PROC          INITINTERRUPTRAM
  C:0066H         LINE#         46
  C:0066H         LINE#         47
  C:0066H         LINE#         48
  C:006AH         LINE#         50
  C:006AH         LINE#         51
  C:006AH         LINE#         52
  C:006AH         LINE#         54
  C:006AH         LINE#         55
  C:006DH         LINE#         57
  C:006EH         LINE#         58
  C:006EH         LINE#         60
  C:006EH         LINE#         61
  C:0071H         LINE#         62
  -------         ENDPROC       INITINTERRUPTRAM
  -------         PROC          EX0_INT
  C:0006H         LINE#         69
  C:0006H         LINE#         71
  C:0008H         LINE#         72
  C:000AH         LINE#         73
  -------         ENDPROC       EX0_INT
  -------         PROC          TIMER0_INT
  C:0492H         LINE#         80
  C:049AH         LINE#         82
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  19:57:05  PAGE 11


  C:049CH         LINE#         83
  C:049EH         LINE#         84
  C:04A1H         LINE#         85
  C:04A4H         LINE#         86
  C:04A6H         LINE#         92
  C:04ACH         LINE#         94
  C:04B2H         LINE#         95
  C:04BFH         LINE#         96
  C:04BFH         LINE#         97
  C:04C1H         LINE#         99
  C:04C2H         LINE#         100
  C:04C7H         LINE#         101
  C:04C7H         LINE#         102
  C:04C9H         LINE#         103
  C:04C9H         LINE#         105
  C:04CFH         LINE#         106
  C:04D9H         LINE#         107
  C:04D9H         LINE#         108
  C:04DBH         LINE#         109
  C:04DEH         LINE#         111
  C:04E2H         LINE#         113
  C:04E5H         LINE#         114
  C:04EAH         LINE#         115
  C:04EAH         LINE#         116
  C:04ECH         LINE#         117
  C:04EFH         LINE#         119
  C:04F5H         LINE#         120
  C:04F8H         LINE#         121
  C:04F8H         LINE#         122
  C:04FBH         LINE#         123
  C:04FDH         LINE#         124
  C:04FDH         LINE#         127
  C:050BH         LINE#         128
  C:051AH         LINE#         129
  C:051AH         LINE#         130
  C:051EH         LINE#         131
  C:0521H         LINE#         133
  C:052FH         LINE#         134
  C:053EH         LINE#         135
  C:053EH         LINE#         136
  C:0542H         LINE#         137
  C:0545H         LINE#         138
  C:0545H         LINE#         139
  C:0545H         LINE#         140
  C:0545H         LINE#         165
  C:0545H         LINE#         166
  -------         ENDPROC       TIMER0_INT
  -------         PROC          EX1_INT
  C:000EH         LINE#         173
  C:000EH         LINE#         175
  C:0010H         LINE#         176
  C:0012H         LINE#         177
  -------         ENDPROC       EX1_INT
  -------         PROC          TIMER1_INT
  C:0842H         LINE#         184
  C:0859H         LINE#         186
  C:085BH         LINE#         187
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  19:57:05  PAGE 12


  C:085DH         LINE#         188
  C:0860H         LINE#         189
  C:0863H         LINE#         190
  C:0865H         LINE#         192
  C:086BH         LINE#         193
  C:0874H         LINE#         195
  C:0882H         LINE#         196
  C:0897H         LINE#         199
  C:089CH         LINE#         200
  C:08A0H         LINE#         201
  C:08A0H         LINE#         202
  -------         ENDPROC       TIMER1_INT
  -------         PROC          EUART0_INT
  -------         DO            
  X:0059H         SYMBOL        TempSBUF
  -------         ENDDO         
  C:0303H         LINE#         297
  C:0312H         LINE#         301
  C:0318H         LINE#         302
  C:0318H         LINE#         303
  C:031AH         LINE#         305
  C:0320H         LINE#         306
  C:0326H         LINE#         308
  C:032DH         LINE#         309
  C:032DH         LINE#         310
  C:0336H         LINE#         311
  C:0336H         LINE#         312
  C:0336H         LINE#         313
  C:0344H         LINE#         314
  C:0347H         LINE#         315
  C:034DH         LINE#         317
  C:0350H         LINE#         319
  C:0350H         LINE#         324
  C:0350H         LINE#         325
  C:0350H         LINE#         326
  C:0355H         LINE#         327
  C:0355H         LINE#         328
  C:035EH         LINE#         329
  C:035EH         LINE#         330
  C:035EH         LINE#         331
  C:035EH         LINE#         332
  C:0361H         LINE#         333
  C:0367H         LINE#         335
  C:0369H         LINE#         337
  C:0369H         LINE#         338
  C:036EH         LINE#         339
  C:0372H         LINE#         340
  C:0376H         LINE#         341
  C:0378H         LINE#         342
  C:0378H         LINE#         343
  C:0378H         LINE#         344
  C:0380H         LINE#         345
  C:0380H         LINE#         346
  C:0385H         LINE#         347
  C:0388H         LINE#         349
  C:0393H         LINE#         350
  C:0393H         LINE#         351
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  19:57:05  PAGE 13


  C:0396H         LINE#         352
  C:0396H         LINE#         353
  C:0399H         LINE#         355
  C:039BH         LINE#         356
  C:039BH         LINE#         357
  C:039DH         LINE#         359
  C:039DH         LINE#         360
  C:039DH         LINE#         361
  C:03A0H         LINE#         362
  C:03A2H         LINE#         363
  C:03A2H         LINE#         364
  C:03A2H         LINE#         366
  C:03A5H         LINE#         367
  C:03A5H         LINE#         368
  C:03A7H         LINE#         370
  C:03B2H         LINE#         371
  C:03B2H         LINE#         372
  C:03B5H         LINE#         373
  C:03C2H         LINE#         374
  C:03C4H         LINE#         376
  C:03C4H         LINE#         377
  C:03C6H         LINE#         378
  C:03C6H         LINE#         379
  C:03C6H         LINE#         380
  -------         ENDPROC       EUART0_INT
  -------         PROC          TIMER2_INT
  C:0016H         LINE#         485
  C:0016H         LINE#         487
  C:0018H         LINE#         488
  C:001AH         LINE#         489
  -------         ENDPROC       TIMER2_INT
  -------         PROC          EX2_INT
  C:0BF7H         LINE#         496
  C:0BF7H         LINE#         498
  C:0BFAH         LINE#         499
  C:0BFDH         LINE#         500
  -------         ENDPROC       EX2_INT
  -------         PROC          SCM_INT
  C:001EH         LINE#         507
  C:001EH         LINE#         509
  C:0021H         LINE#         510
  -------         ENDPROC       SCM_INT
  -------         PROC          PWM_INT
  C:0026H         LINE#         517
  C:0026H         LINE#         519
  C:0029H         LINE#         520
  -------         ENDPROC       PWM_INT
  -------         PROC          ELPD_INT
  C:005EH         LINE#         527
  C:005EH         LINE#         529
  C:0061H         LINE#         530
  -------         ENDPROC       ELPD_INT
  -------         ENDMOD        INTERRUPT

  -------         MODULE        LEDAPP
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  19:57:05  PAGE 14


  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  X:005AH         PUBLIC        LedWorkGroupStr
  C:0C0AH         PUBLIC        InitAllLedRamApp
  C:0756H         PUBLIC        LedApp
  -------         PROC          INITALLLEDRAMAPP
  C:0C0AH         LINE#         50
  C:0C0AH         LINE#         51
  C:0C0AH         LINE#         52
  -------         ENDPROC       INITALLLEDRAMAPP
  -------         PROC          LEDAPP
  C:0756H         LINE#         60
  C:0756H         LINE#         61
  C:0756H         LINE#         62
  C:075EH         LINE#         63
  C:075EH         LINE#         64
  C:075FH         LINE#         66
  C:0777H         LINE#         67
  C:0777H         LINE#         68
  C:0777H         LINE#         69
  C:077AH         LINE#         70
  C:077DH         LINE#         71
  C:077DH         LINE#         73
  C:077DH         LINE#         74
  C:0780H         LINE#         75
  C:0783H         LINE#         76
  C:0786H         LINE#         77
  C:0786H         LINE#         79
  C:0786H         LINE#         80
  C:0789H         LINE#         81
  C:078CH         LINE#         82
  C:078FH         LINE#         83
  C:0792H         LINE#         84
  C:0792H         LINE#         86
  C:0792H         LINE#         87
  C:0795H         LINE#         88
  C:0798H         LINE#         89
  C:079BH         LINE#         90
  C:079EH         LINE#         91
  C:07A1H         LINE#         92
  C:07A1H         LINE#         94
  C:07A1H         LINE#         95
  C:07A4H         LINE#         96
  C:07A7H         LINE#         97
  C:07AAH         LINE#         98
  C:07ADH         LINE#         99
  C:07B0H         LINE#         100
  C:07B3H         LINE#         101
  C:07B3H         LINE#         103
  C:07B3H         LINE#         104
  C:07B6H         LINE#         105
  C:07B9H         LINE#         106
  C:07BCH         LINE#         107
  C:07BFH         LINE#         108
  C:07C2H         LINE#         109
  C:07C5H         LINE#         110
  C:07C8H         LINE#         111
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  19:57:05  PAGE 15


  C:07C8H         LINE#         113
  C:07C8H         LINE#         114
  C:07CBH         LINE#         115
  C:07CBH         LINE#         116
  C:07CBH         LINE#         117
  C:07CBH         LINE#         118
  -------         ENDPROC       LEDAPP
  -------         ENDMOD        LEDAPP

  -------         MODULE        KEYAPP
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  C:05DAH         PUBLIC        KeyProc
  B:0098H.4       PUBLIC        REN
  X:0060H         PUBLIC        g_last_speed_level
  C:0BFEH         PUBLIC        InitKeyProcRam
  X:0061H         PUBLIC        KeyProcStr
  C:0A7EH         SYMBOL        Com000F
  C:0A7EH         SYMBOL        L?0016
  -------         PROC          COM000F
  -------         ENDPROC       COM000F
  -------         PROC          INITKEYPROCRAM
  C:0BFEH         LINE#         40
  C:0BFEH         LINE#         41
  C:0BFEH         LINE#         42
  C:0C03H         LINE#         43
  -------         ENDPROC       INITKEYPROCRAM
  C:0659H         SYMBOL        L?0017
  -------         PROC          KEYPROC
  C:05DAH         LINE#         50
  C:05DAH         LINE#         51
  C:05DAH         LINE#         52
  C:05E2H         LINE#         53
  C:05E2H         LINE#         54
  C:05E3H         LINE#         56
  C:05EBH         LINE#         57
  C:05EBH         LINE#         58
  C:05ECH         LINE#         59
  C:05F4H         LINE#         60
  C:05F4H         LINE#         61
  C:05F5H         LINE#         64
  C:05FBH         LINE#         65
  C:0605H         LINE#         66
  C:0605H         LINE#         67
  C:0608H         LINE#         68
  C:0608H         LINE#         70
  C:0608H         LINE#         72
  C:0608H         LINE#         73
  C:0608H         LINE#         74
  C:0608H         LINE#         75
  C:0608H         LINE#         78
  C:060DH         LINE#         79
  C:060DH         LINE#         80
  C:060DH         LINE#         81
  C:0610H         LINE#         82
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  19:57:05  PAGE 16


  C:0610H         LINE#         83
  C:0610H         LINE#         84
  C:0610H         LINE#         85
  C:0610H         LINE#         87
  C:0618H         LINE#         88
  C:0618H         LINE#         89
  C:0619H         LINE#         91
  C:0621H         LINE#         92
  C:0621H         LINE#         93
  C:0622H         LINE#         94
  C:062AH         LINE#         95
  C:062AH         LINE#         96
  C:062BH         LINE#         98
  C:063BH         LINE#         99
  C:063BH         LINE#         100
  C:063EH         LINE#         101
  C:0640H         LINE#         102
  C:064BH         LINE#         103
  C:064BH         LINE#         104
  C:0650H         LINE#         105
  C:0650H         LINE#         107
  C:0650H         LINE#         109
  C:0650H         LINE#         110
  C:0650H         LINE#         111
  C:0650H         LINE#         112
  C:0650H         LINE#         115
  C:0655H         LINE#         116
  C:0655H         LINE#         117
  C:0655H         LINE#         118
  C:0658H         LINE#         119
  C:0658H         LINE#         120
  C:0658H         LINE#         121
  C:0658H         LINE#         122
  C:0658H         LINE#         123
  -------         ENDPROC       KEYPROC
  -------         ENDMOD        KEYAPP

  -------         MODULE        COMMUNICATION
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  X:0000H         PUBLIC        Uart
  C:0A29H         PUBLIC        _CalcChecksum
  D:0099H         PUBLIC        SBUF
  C:03D3H         PUBLIC        _TxdDataProc
  C:0AC1H         PUBLIC        CommFunProc
  C:07CCH         PUBLIC        CommDealResponse
  B:0098H.4       PUBLIC        REN
  C:0B42H         SYMBOL        Com003C
  C:0B42H         SYMBOL        L?0061
  C:0B45H         SYMBOL        L?0062
  -------         PROC          COM003C
  -------         ENDPROC       COM003C
  -------         PROC          _CALCCHECKSUM
  X:0056H         SYMBOL        CS_Buffer
  D:0005H         SYMBOL        Len
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  19:57:05  PAGE 17


  -------         DO            
  D:0007H         SYMBOL        CS
  D:0006H         SYMBOL        j
  -------         ENDDO         
  C:0A29H         LINE#         104
  C:0A34H         LINE#         105
  C:0A34H         LINE#         106
  C:0A36H         LINE#         107
  C:0A37H         LINE#         109
  C:0A3CH         LINE#         110
  C:0A3CH         LINE#         111
  C:0A51H         LINE#         112
  C:0A54H         LINE#         113
  C:0A54H         LINE#         114
  -------         ENDPROC       _CALCCHECKSUM
  -------         PROC          COMMFUNPROC
  C:0AC1H         LINE#         121
  C:0AC1H         LINE#         122
  C:0AC1H         LINE#         123
  C:0AC9H         LINE#         124
  C:0AC9H         LINE#         125
  C:0ACAH         LINE#         153
  C:0ACFH         LINE#         156
  C:0AD7H         LINE#         157
  C:0AD7H         LINE#         158
  C:0ADAH         LINE#         159
  C:0ADAH         LINE#         161
  C:0ADAH         LINE#         162
  -------         ENDPROC       COMMFUNPROC
  C:0486H         SYMBOL        L?0063
  -------         PROC          _TXDDATAPROC
  X:0055H         SYMBOL        Ack
  -------         DO            
  D:0001H         SYMBOL        checksum
  -------         ENDDO         
  C:03D3H         LINE#         169
  C:03D8H         LINE#         170
  C:03D8H         LINE#         172
  C:03D8H         LINE#         174
  C:03DDH         LINE#         175
  C:03DDH         LINE#         176
  C:03DFH         LINE#         177
  C:03DFH         LINE#         179
  C:03F4H         LINE#         180
  C:03F4H         LINE#         181
  C:03F4H         LINE#         182
  C:03F4H         LINE#         183
  C:03F4H         LINE#         184
  C:03F4H         LINE#         185
  C:03F4H         LINE#         186
  C:03F6H         LINE#         188
  C:03F6H         LINE#         189
  C:03FAH         LINE#         190
  C:03FCH         LINE#         192
  C:03FCH         LINE#         193
  C:0401H         LINE#         194
  C:0401H         LINE#         195
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  19:57:05  PAGE 18


  C:0401H         LINE#         198
  C:0401H         LINE#         199
  C:0401H         LINE#         207
  C:0403H         LINE#         208
  C:040BH         LINE#         209
  C:040BH         LINE#         210
  C:040DH         LINE#         212
  C:040DH         LINE#         214
  C:0422H         LINE#         215
  C:0422H         LINE#         216
  C:0422H         LINE#         217
  C:0422H         LINE#         218
  C:0422H         LINE#         219
  C:0422H         LINE#         220
  C:0422H         LINE#         221
  C:0424H         LINE#         223
  C:0424H         LINE#         224
  C:0428H         LINE#         225
  C:042AH         LINE#         227
  C:042AH         LINE#         228
  C:042FH         LINE#         229
  C:042FH         LINE#         230
  C:042FH         LINE#         233
  C:0434H         LINE#         234
  C:0439H         LINE#         242
  C:043BH         LINE#         243
  C:0443H         LINE#         244
  C:0443H         LINE#         245
  C:0448H         LINE#         247
  C:0448H         LINE#         249
  C:045DH         LINE#         250
  C:045DH         LINE#         251
  C:045DH         LINE#         252
  C:045DH         LINE#         253
  C:045DH         LINE#         254
  C:045DH         LINE#         255
  C:045DH         LINE#         256
  C:045FH         LINE#         258
  C:045FH         LINE#         259
  C:0463H         LINE#         260
  C:0465H         LINE#         262
  C:0465H         LINE#         263
  C:046AH         LINE#         264
  C:046AH         LINE#         265
  C:046AH         LINE#         268
  C:046DH         LINE#         269
  C:0472H         LINE#         277
  C:0472H         LINE#         279
  C:0477H         LINE#         280
  C:047DH         LINE#         281
  C:047FH         LINE#         282
  C:0485H         LINE#         283
  -------         ENDPROC       _TXDDATAPROC
  -------         PROC          COMMDEALRESPONSE
  -------         DO            
  D:0007H         SYMBOL        rechecksum
  -------         ENDDO         
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  19:57:05  PAGE 19


  C:07CCH         LINE#         290
  C:07CCH         LINE#         291
  C:07CCH         LINE#         294
  C:07D4H         LINE#         295
  C:07D4H         LINE#         296
  C:07DFH         LINE#         298
  C:07E6H         LINE#         299
  C:07E6H         LINE#         301
  C:07FEH         LINE#         302
  C:07FEH         LINE#         303
  C:07FEH         LINE#         304
  C:0803H         LINE#         305
  C:0803H         LINE#         306
  C:0805H         LINE#         307
  C:0805H         LINE#         308
  C:080AH         LINE#         309
  C:080AH         LINE#         310
  C:080CH         LINE#         311
  C:080CH         LINE#         312
  C:0811H         LINE#         313
  C:0811H         LINE#         314
  C:0813H         LINE#         315
  C:0813H         LINE#         316
  C:0818H         LINE#         317
  C:0818H         LINE#         318
  C:081AH         LINE#         319
  C:081AH         LINE#         320
  C:081FH         LINE#         321
  C:081FH         LINE#         322
  C:0821H         LINE#         323
  C:0821H         LINE#         324
  C:0827H         LINE#         325
  C:082BH         LINE#         326
  C:082BH         LINE#         327
  C:082BH         LINE#         328
  C:082BH         LINE#         329
  C:082BH         LINE#         332
  C:082DH         LINE#         333
  C:082FH         LINE#         335
  C:082FH         LINE#         337
  C:0834H         LINE#         338
  C:0834H         LINE#         341
  C:0839H         LINE#         342
  C:083DH         LINE#         343
  C:0841H         LINE#         344
  C:0841H         LINE#         345
  -------         ENDPROC       COMMDEALRESPONSE
  -------         ENDMOD        COMMUNICATION

  -------         MODULE        LEDDRIVER
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:00EAH         PUBLIC        P1M0
  D:00E2H         PUBLIC        P1M1
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  B:0090H.2       PUBLIC        P1_2
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  19:57:05  PAGE 20


  B:0090H.3       PUBLIC        P1_3
  B:0090H.4       PUBLIC        P1_4
  B:0090H.5       PUBLIC        P1_5
  B:0090H.6       PUBLIC        P1_6
  B:0090H.7       PUBLIC        P1_7
  C:0B7EH         PUBLIC        LED1_WorkOff
  C:0B90H         PUBLIC        LED2_WorkOff
  C:0BA2H         PUBLIC        LED3_WorkOff
  C:0BB4H         PUBLIC        LED4_WorkOff
  C:0BC6H         PUBLIC        LED5_WorkOff
  C:0BD8H         PUBLIC        LED6_WorkOff
  C:0B75H         PUBLIC        LED1_WorkOn
  C:0B87H         PUBLIC        LED2_WorkOn
  C:0B99H         PUBLIC        LED3_WorkOn
  C:0BABH         PUBLIC        LED4_WorkOn
  C:0BBDH         PUBLIC        LED5_WorkOn
  C:0BCFH         PUBLIC        LED6_WorkOn
  C:0C04H         PUBLIC        InitAllLedDriverRam
  C:0B30H         PUBLIC        InitAllLedOff
  -------         PROC          INITALLLEDDRIVERRAM
  C:0C04H         LINE#         49
  C:0C04H         LINE#         50
  C:0C04H         LINE#         51
  C:0C09H         LINE#         65
  -------         ENDPROC       INITALLLEDDRIVERRAM
  -------         PROC          INITALLLEDOFF
  C:0B30H         LINE#         72
  C:0B30H         LINE#         73
  C:0B30H         LINE#         74
  C:0B33H         LINE#         75
  C:0B36H         LINE#         76
  C:0B39H         LINE#         77
  C:0B3CH         LINE#         78
  C:0B3FH         LINE#         79
  -------         ENDPROC       INITALLLEDOFF
  -------         PROC          LED1_WORKON
  C:0B75H         LINE#         88
  C:0B75H         LINE#         89
  C:0B75H         LINE#         90
  C:0B7BH         LINE#         91
  C:0B7DH         LINE#         92
  -------         ENDPROC       LED1_WORKON
  -------         PROC          LED1_WORKOFF
  C:0B7EH         LINE#         99
  C:0B7EH         LINE#         100
  C:0B7EH         LINE#         101
  C:0B84H         LINE#         102
  C:0B86H         LINE#         103
  -------         ENDPROC       LED1_WORKOFF
  -------         PROC          LED2_WORKON
  C:0B87H         LINE#         131
  C:0B87H         LINE#         132
  C:0B87H         LINE#         133
  C:0B8DH         LINE#         134
  C:0B8FH         LINE#         135
  -------         ENDPROC       LED2_WORKON
  -------         PROC          LED2_WORKOFF
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  19:57:05  PAGE 21


  C:0B90H         LINE#         142
  C:0B90H         LINE#         143
  C:0B90H         LINE#         144
  C:0B96H         LINE#         145
  C:0B98H         LINE#         146
  -------         ENDPROC       LED2_WORKOFF
  -------         PROC          LED3_WORKON
  C:0B99H         LINE#         175
  C:0B99H         LINE#         176
  C:0B99H         LINE#         177
  C:0B9FH         LINE#         178
  C:0BA1H         LINE#         179
  -------         ENDPROC       LED3_WORKON
  -------         PROC          LED3_WORKOFF
  C:0BA2H         LINE#         186
  C:0BA2H         LINE#         187
  C:0BA2H         LINE#         188
  C:0BA8H         LINE#         189
  C:0BAAH         LINE#         190
  -------         ENDPROC       LED3_WORKOFF
  -------         PROC          LED4_WORKON
  C:0BABH         LINE#         218
  C:0BABH         LINE#         219
  C:0BABH         LINE#         220
  C:0BB1H         LINE#         221
  C:0BB3H         LINE#         222
  -------         ENDPROC       LED4_WORKON
  -------         PROC          LED4_WORKOFF
  C:0BB4H         LINE#         229
  C:0BB4H         LINE#         230
  C:0BB4H         LINE#         231
  C:0BBAH         LINE#         232
  C:0BBCH         LINE#         233
  -------         ENDPROC       LED4_WORKOFF
  -------         PROC          LED5_WORKON
  C:0BBDH         LINE#         262
  C:0BBDH         LINE#         263
  C:0BBDH         LINE#         264
  C:0BC3H         LINE#         265
  C:0BC5H         LINE#         266
  -------         ENDPROC       LED5_WORKON
  -------         PROC          LED5_WORKOFF
  C:0BC6H         LINE#         273
  C:0BC6H         LINE#         274
  C:0BC6H         LINE#         275
  C:0BCCH         LINE#         276
  C:0BCEH         LINE#         277
  -------         ENDPROC       LED5_WORKOFF
  -------         PROC          LED6_WORKON
  C:0BCFH         LINE#         305
  C:0BCFH         LINE#         306
  C:0BCFH         LINE#         307
  C:0BD5H         LINE#         308
  C:0BD7H         LINE#         309
  -------         ENDPROC       LED6_WORKON
  -------         PROC          LED6_WORKOFF
  C:0BD8H         LINE#         316
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  19:57:05  PAGE 22


  C:0BD8H         LINE#         317
  C:0BD8H         LINE#         318
  C:0BDEH         LINE#         319
  C:0BE0H         LINE#         320
  -------         ENDPROC       LED6_WORKOFF
  -------         ENDMOD        LEDDRIVER

  -------         MODULE        KEYDRIVER
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:00ECH         PUBLIC        P3M0
  D:0090H         PUBLIC        P1
  D:00E4H         PUBLIC        P3M1
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  X:0048H         PUBLIC        KeyDrvStr
  B:00B0H.3       PUBLIC        P3_3
  B:00B0H.7       PUBLIC        P3_7
  C:01C6H         PUBLIC        KeyScan
  C:08B5H         PUBLIC        InitKeyScanRam
  C:0B5FH         SYMBOL        Com002D
  C:0B5FH         SYMBOL        L?0046
  -------         PROC          COM002D
  -------         ENDPROC       COM002D
  -------         PROC          INITKEYSCANRAM
  -------         DO            
  D:0007H         SYMBOL        i
  -------         ENDDO         
  C:08B5H         LINE#         48
  C:08B5H         LINE#         49
  C:08B5H         LINE#         52
  C:08BAH         LINE#         53
  C:08C5H         LINE#         54
  C:08C5H         LINE#         55
  C:08D1H         LINE#         56
  C:08DDH         LINE#         57
  C:08E9H         LINE#         58
  C:08F5H         LINE#         59
  C:0901H         LINE#         60
  C:090DH         LINE#         61
  C:0910H         LINE#         62
  -------         ENDPROC       INITKEYSCANRAM
  -------         PROC          KEYSCAN
  -------         DO            
  X:0055H         SYMBOL        tempbuff
  -------         ENDDO         
  C:01C6H         LINE#         69
  C:01C6H         LINE#         70
  C:01C6H         LINE#         73
  C:01D1H         LINE#         74
  C:01D1H         LINE#         75
  C:01D2H         LINE#         77
  C:01D8H         LINE#         78
  C:01DEH         LINE#         79
  C:01E0H         LINE#         80
  C:01E2H         LINE#         81
  C:01E3H         LINE#         82
  C:01E4H         LINE#         83
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  19:57:05  PAGE 23


  C:01E5H         LINE#         84
  C:01E6H         LINE#         85
  C:01E7H         LINE#         86
  C:01E8H         LINE#         87
  C:01E9H         LINE#         88
  C:01EAH         LINE#         89
  C:01EBH         LINE#         90
  C:01ECH         LINE#         91
  C:01EFH         LINE#         92
  C:01EFH         LINE#         93
  C:01F4H         LINE#         94
  C:01F6H         LINE#         96
  C:01F6H         LINE#         97
  C:01FBH         LINE#         98
  C:01FBH         LINE#         99
  C:01FEH         LINE#         100
  C:01FEH         LINE#         101
  C:0204H         LINE#         102
  C:0206H         LINE#         104
  C:0206H         LINE#         105
  C:020BH         LINE#         106
  C:020BH         LINE#         110
  C:0217H         LINE#         111
  C:0217H         LINE#         112
  C:0219H         LINE#         113
  C:021EH         LINE#         114
  C:0220H         LINE#         116
  C:0220H         LINE#         117
  C:0223H         LINE#         118
  C:0228H         LINE#         119
  C:0228H         LINE#         120
  C:022AH         LINE#         122
  C:0237H         LINE#         123
  C:0237H         LINE#         124
  C:023CH         LINE#         126
  C:023FH         LINE#         127
  C:023FH         LINE#         128
  C:0245H         LINE#         129
  C:0247H         LINE#         130
  C:024EH         LINE#         131
  C:024EH         LINE#         132
  C:0251H         LINE#         133
  C:0257H         LINE#         134
  C:025AH         LINE#         135
  C:025CH         LINE#         137
  C:025CH         LINE#         138
  C:025FH         LINE#         139
  C:025FH         LINE#         140
  C:0261H         LINE#         142
  C:0261H         LINE#         143
  C:0265H         LINE#         144
  C:0265H         LINE#         145
  C:026BH         LINE#         146
  C:026DH         LINE#         147
  C:0270H         LINE#         148
  C:0270H         LINE#         149
  C:0276H         LINE#         150
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  19:57:05  PAGE 24


  C:0278H         LINE#         152
  C:0278H         LINE#         153
  C:027DH         LINE#         154
  C:0281H         LINE#         155
  C:0285H         LINE#         156
  C:0289H         LINE#         157
  C:0289H         LINE#         158
  C:0289H         LINE#         159
  C:0289H         LINE#         160
  C:0289H         LINE#         163
  C:0295H         LINE#         164
  C:0295H         LINE#         165
  C:0297H         LINE#         166
  C:029CH         LINE#         167
  C:029DH         LINE#         169
  C:029DH         LINE#         170
  C:02A0H         LINE#         171
  C:02A5H         LINE#         172
  C:02A5H         LINE#         173
  C:02A7H         LINE#         175
  C:02B4H         LINE#         176
  C:02B4H         LINE#         177
  C:02B9H         LINE#         179
  C:02BCH         LINE#         180
  C:02BCH         LINE#         181
  C:02C2H         LINE#         182
  C:02C3H         LINE#         183
  C:02CAH         LINE#         184
  C:02CAH         LINE#         185
  C:02CDH         LINE#         186
  C:02D3H         LINE#         187
  C:02D6H         LINE#         188
  C:02D8H         LINE#         190
  C:02D8H         LINE#         191
  C:02DBH         LINE#         192
  C:02DBH         LINE#         193
  C:02DCH         LINE#         195
  C:02DCH         LINE#         196
  C:02E0H         LINE#         197
  C:02E0H         LINE#         198
  C:02E6H         LINE#         199
  C:02E7H         LINE#         200
  C:02EAH         LINE#         201
  C:02EAH         LINE#         202
  C:02F0H         LINE#         203
  C:02F1H         LINE#         205
  C:02F1H         LINE#         206
  C:02F6H         LINE#         207
  C:02FAH         LINE#         208
  C:02FEH         LINE#         209
  C:0302H         LINE#         210
  C:0302H         LINE#         211
  C:0302H         LINE#         212
  C:0302H         LINE#         213
  C:0302H         LINE#         214
  C:0302H         LINE#         215
  -------         ENDPROC       KEYSCAN
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  19:57:05  PAGE 25


  -------         ENDMOD        KEYDRIVER

  -------         MODULE        UARTDRIVER
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:00ECH         PUBLIC        P3M0
  D:0090H         PUBLIC        P1
  D:00E4H         PUBLIC        P3M1
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  D:00A8H         PUBLIC        IEN0
  C:09AFH         PUBLIC        _Uart0CalBaudrate
  B:0098H.0       PUBLIC        RI
  D:00CBH         PUBLIC        RCAP2H
  D:00CAH         PUBLIC        RCAP2L
  D:0087H         PUBLIC        PCON
  D:0098H         PUBLIC        SCON
  D:00CDH         PUBLIC        TH2
  D:00CCH         PUBLIC        TL2
  B:00C8H.2       PUBLIC        TR2
  C:0A55H         PUBLIC        Uart0Init
  B:0098H.4       PUBLIC        REN
  D:00C9H         PUBLIC        T2MOD
  D:00C8H         PUBLIC        T2CON
  D:009BH         PUBLIC        SADEN
  D:009AH         PUBLIC        SADDR
  -------         PROC          UART0INIT
  C:0A55H         LINE#         49
  C:0A55H         LINE#         50
  C:0A55H         LINE#         51
  C:0A58H         LINE#         52
  C:0A5BH         LINE#         53
  C:0A5EH         LINE#         59
  C:0A61H         LINE#         60
  C:0A64H         LINE#         61
  C:0A67H         LINE#         63
  C:0A6AH         LINE#         64
  C:0A6CH         LINE#         66
  C:0A73H         LINE#         67
  C:0A76H         LINE#         68
  C:0A78H         LINE#         69
  C:0A7AH         LINE#         70
  C:0A7DH         LINE#         71
  -------         ENDPROC       UART0INIT
  -------         PROC          _UART0CALBAUDRATE
  D:0006H         SYMBOL        baudratepar
  -------         DO            
  X:0055H         SYMBOL        CalBaudRateTemp
  -------         ENDDO         
  C:09AFH         LINE#         80
  C:09AFH         LINE#         81
  C:09AFH         LINE#         84
  C:09CDH         LINE#         89
  C:09D0H         LINE#         90
  C:09D3H         LINE#         91
  C:09D6H         LINE#         92
  C:09D9H         LINE#         93
  C:09DCH         LINE#         94
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  19:57:05  PAGE 26


  C:09DFH         LINE#         95
  C:09E2H         LINE#         96
  C:09E4H         LINE#         97
  C:09E6H         LINE#         99
  C:09E9H         LINE#         100
  C:09ECH         LINE#         101
  C:09EEH         LINE#         102
  -------         ENDPROC       _UART0CALBAUDRATE
  -------         ENDMOD        UARTDRIVER

  -------         MODULE        PWMDRIVER
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  C:0665H         PUBLIC        Timer1_INT
  D:00B0H         PUBLIC        P3
  D:00C0H         PUBLIC        P4
  B:00A8H.7       PUBLIC        EA
  X:005DH         PUBLIC        count
  X:005EH         PUBLIC        high_count
  D:0089H         PUBLIC        TMOD
  X:005FH         PUBLIC        duty
  B:00A8H.3       PUBLIC        ET1
  B:0088H.7       PUBLIC        TF1
  C:0B1DH         PUBLIC        Timer1Init
  D:008DH         PUBLIC        TH1
  D:008BH         PUBLIC        TL1
  B:0088H.6       PUBLIC        TR1
  B:00B0H.0       PUBLIC        PWM_IN
  C:06DFH         PUBLIC        _DutyDecode
  -------         PROC          TIMER1_INT
  C:0665H         LINE#         11
  C:0682H         LINE#         12
  C:0684H         LINE#         13
  C:0686H         LINE#         14
  C:0689H         LINE#         15
  C:068CH         LINE#         16
  C:068EH         LINE#         18
  C:0694H         LINE#         19
  C:069BH         LINE#         21
  C:06A9H         LINE#         22
  C:06BAH         LINE#         25
  C:06BDH         LINE#         28
  C:06C2H         LINE#         29
  C:06C4H         LINE#         30
  C:06C4H         LINE#         31
  -------         ENDPROC       TIMER1_INT
  -------         PROC          _DUTYDECODE
  D:0007H         SYMBOL        duty_value
  C:06DFH         LINE#         34
  C:06DFH         LINE#         35
  C:06ECH         LINE#         36
  C:06F1H         LINE#         37
  C:06F1H         LINE#         38
  C:06F3H         LINE#         39
  C:06FEH         LINE#         40
  C:0703H         LINE#         41
  C:0703H         LINE#         42
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  19:57:05  PAGE 27


  C:0705H         LINE#         43
  C:0710H         LINE#         44
  C:0715H         LINE#         45
  C:0715H         LINE#         46
  C:0717H         LINE#         47
  C:0722H         LINE#         48
  C:0727H         LINE#         49
  C:0727H         LINE#         50
  C:0729H         LINE#         51
  C:0734H         LINE#         52
  C:073AH         LINE#         53
  C:073AH         LINE#         54
  C:073CH         LINE#         55
  C:074CH         LINE#         56
  C:074DH         LINE#         57
  C:074DH         LINE#         58
  C:074FH         LINE#         59
  C:074FH         LINE#         61
  C:0750H         LINE#         62
  C:0755H         LINE#         63
  C:0755H         LINE#         64
  -------         ENDPROC       _DUTYDECODE
  -------         PROC          TIMER1INIT
  C:0B1DH         LINE#         67
  C:0B1DH         LINE#         68
  C:0B1DH         LINE#         69
  C:0B20H         LINE#         70
  C:0B23H         LINE#         71
  C:0B26H         LINE#         72
  C:0B29H         LINE#         73
  C:0B2BH         LINE#         74
  C:0B2DH         LINE#         75
  C:0B2FH         LINE#         76
  -------         ENDPROC       TIMER1INIT
  -------         ENDMOD        PWMDRIVER

  -------         MODULE        ?C?CLDOPTR
  C:0076H         PUBLIC        ?C?CLDOPTR
  -------         ENDMOD        ?C?CLDOPTR

  -------         MODULE        ?C?UIDIV
  C:00A3H         PUBLIC        ?C?UIDIV
  -------         ENDMOD        ?C?UIDIV

  -------         MODULE        ?C?SLDIV
  C:096BH         PUBLIC        ?C?SLDIV
  -------         ENDMOD        ?C?SLDIV

  -------         MODULE        ?C?ULDIV
  C:0134H         PUBLIC        ?C?ULDIV
  -------         ENDMOD        ?C?ULDIV

*** ERROR L104: MULTIPLE PUBLIC DEFINITIONS
    SYMBOL:  TIMER1_INT
    MODULE:  .\output\PwmDriver.obj (PWMDRIVER)

*** WARNING L5: CODE SPACE MEMORY OVERLAP
BL51 BANKED LINKER/LOCATER V6.22.2.0                                                  06/24/2025  19:57:05  PAGE 28


    FROM:    001BH
    TO:      001DH

*** WARNING L1: UNRESOLVED EXTERNAL SYMBOL
    SYMBOL:  _PWMDECODE
    MODULE:  .\output\Main.obj (MAIN)

*** WARNING L2: REFERENCE MADE TO UNRESOLVED EXTERNAL
    SYMBOL:  _PWMDECODE
    MODULE:  .\output\Main.obj (MAIN)
    ADDRESS: 0964H

Program Size: data=9.0 xdata=99 code=3084
LINK/LOCATE RUN COMPLETE.  3 WARNING(S),  1 ERROR(S)
